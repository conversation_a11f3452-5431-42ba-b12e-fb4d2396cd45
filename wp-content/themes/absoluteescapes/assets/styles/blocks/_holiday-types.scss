.holiday-types {
    a {
        font-weight: normal;
        text-decoration: none;

        &:hover,
        &:focus {
            text-decoration: none;

            .holiday-types__image {
                &:before {
                    background: rgba($black, 0.2);
                }
            }

            .holiday-types__heading {
                // Keep white color when in overlay
                .holiday-types__image-overlay & {
                    color: $white;
                }
            }

            .link {
                color: $blue;
            }
        }
    }

    &__inner {
        padding: 60px 0;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 45px 0;
        }
    }

    &__row {
        display: flex;
        flex-wrap: wrap;
        align-items: stretch;
        gap: 50px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            gap: 30px;
        }

        // Single column layout on mobile/tablet (up to 991px)
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex-direction: column;
        }

        // Use CSS Grid from desktop width for equal height content rows
        @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
            display: grid;
            grid-template-columns: 1fr 1fr;
            align-items: stretch;
        }
    }

    &__content {
        padding: 15px 0;
        height: 100%;
        display: flex;
        flex-direction: column;

        // From desktop width, ensure content fills grid cell height
        @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
            height: 100%;
            align-self: stretch;
        }

        // Tablet padding
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) and (min-width: map-get($grid-breakpoints, sm)) {
            padding: 30px 0 15px 0;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 30px 0 45px;
        }
    }

    &__link {
        display: flex;
        flex-direction: column;
        height: 100%;

        // From desktop width, ensure link fills content height
        @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
            height: 100%;
        }
    }

    &__col {
        position: relative;
        flex: 1;

        &:after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            width: 100%;
            border-bottom: 1px solid $midlightgrey;

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                width: calc(108% + 5px);
                left: calc(-4% - 5px);
            }
        }

        .holiday-types__content-col {
            flex: 0 0 50%;
            max-width: 50%;
            display: flex;
            flex-direction: column;

            // On tablet (576px to 991px), override Bootstrap flex properties for CSS Grid
            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) and (min-width: map-get($grid-breakpoints, sm)) {
                flex: none !important;
                max-width: none !important;
                padding: 0 !important;

                &:last-child {
                    padding-left: 0 !important; // Remove left padding since grid gap handles spacing
                }
            }

            &:last-child {
                padding-left: 30px;

                @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                    padding-left: 20px;
                }

                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    padding-left: 0;
                    padding-top: 20px;
                }
            }
        }

        &:first-child,
        &:nth-child(2) {
            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                .holiday-types__content-col {
                    flex: 0 0 100%;
                    max-width: 100%;

                    &:nth-child(1) {
                        order: 1;
                    }

                    &:nth-child(2) {
                        order: 2;
                    }

                    .holiday-types__image {
                        text-align: left;
                    }
                }
            }
        }

        &:last-child,
        &:nth-last-child(2) {
            @media only screen and (min-width: map-get($grid-breakpoints, md)) {
                &:after {
                    display: none;
                }
            }
        }


    }

    &__content-row {
        align-items: stretch;
        height: 100%;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            align-items: flex-start;
        }

        // On tablet, use CSS Grid to make image and text columns equal height
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) and (min-width: map-get($grid-breakpoints, sm)) {
            display: grid !important; // Override Bootstrap's flex
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 0; // Remove Bootstrap row margins
            align-items: stretch;
        }

        // From desktop width, use CSS Grid to ensure equal height content rows
        @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
            height: 100%;
            display: grid;
            grid-template-columns: 1fr 1fr;
            align-items: stretch;
            margin: 0; // Remove Bootstrap row margins

            // Override Bootstrap column classes to use grid
            .holiday-types__content-col {
                flex: none;
                max-width: none;
                padding: 0;

                &:last-child {
                    padding-left: 30px;
                }
            }
        }
    }

    &__heading {
        transition: 300ms;

        // Mobile font size for all headings
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            font-size: 24px;
        }

        // When inside image overlay
        .holiday-types__image-overlay & {
            color: $white;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                font-size: 24px;
            }
        }
    }

    &__image-wrapper {
        position: relative;
        overflow: hidden; // Contain the image overlay within wrapper bounds
        display: flex;
        flex-direction: column;

        // Full height only on desktop where grid controls equal heights
        @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
            height: 100%;
        }

        // On tablet (576px to 991px), make wrapper relative for absolute positioning
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) and (min-width: map-get($grid-breakpoints, sm)) {
            position: relative;
            height: 100%;
        }

        // Mobile full height for better layout
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            height: 100%;
        }
    }

    &__image {
        position: relative;
        text-align: left;
        display: flex;
        flex-direction: column;

        // From desktop width, ensure image containers stretch to match content height
        @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
            height: 100%;
            align-self: stretch;
            flex: 1;
        }

        // On tablet (576px to 991px), make container relative for absolute positioning
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) and (min-width: map-get($grid-breakpoints, sm)) {
            position: relative;
            height: 100%;
        }

        // Mobile behavior
        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            flex: 1;
        }

        img {
            object-fit: cover;
            width: 100%;
            height: 100%;
            display: block;
            position: relative;

            // From desktop width, ensure images fill available height and match tallest content
            @media only screen and (min-width: map-get($grid-breakpoints, lg)) {
                height: 100%;
                min-height: 100%;
                object-fit: cover;
            }

            // On tablet (576px to 991px), absolutely position image to not affect parent height
            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) and (min-width: map-get($grid-breakpoints, sm)) {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            // Mobile minimum height for usability
            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                min-height: 200px;
            }
        }

        // Create overlay that matches the image dimensions exactly
        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba($black, 0);
            transition: 300ms;
            z-index: 3;
            pointer-events: none;
        }
    }

    &__image-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 2;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        padding: 40px 20px 20px;
        pointer-events: none; // Allow clicks to pass through to the link
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 30px 15px 15px;
        }
    }

    &__icon-wrapper {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    &__icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #3e5056;

        img {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1);
        }

        svg {
            width: 22px;
            height: 22px;
            fill: white;
            color: white;
        }

        i {
            font-size: 18px;
            color: white;
        }
    }



    &__text {
        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding: 8px 0;
        }
    }

    &__copy {
        p,
        li {
            color: $bluegrey;
        }
    }


}
